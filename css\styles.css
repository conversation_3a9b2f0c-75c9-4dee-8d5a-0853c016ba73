/* Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4895ef;
    --text-color: #333;
    --text-light: #777;
    --bg-color: #f8f9fa;
    --sidebar-bg: #fff;
    --card-bg: #fff;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

button {
    cursor: pointer;
    border: none;
    background: none;
}

/* Layout */
.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--sidebar-bg);
    box-shadow: var(--shadow);
    padding: 20px 0;
    transition: var(--transition);
    z-index: 100;
}

.logo {
    padding: 0 20px 20px;
    border-bottom: 1px solid var(--border-color);
}

.logo h2 {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-links li {
    position: relative;
    transition: var(--transition);
}

.nav-links li.active {
    background-color: rgba(67, 97, 238, 0.1);
}

.nav-links li.active a {
    color: var(--primary-color);
}

.nav-links li a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--text-color);
    transition: var(--transition);
}

.nav-links li a:hover {
    background-color: rgba(67, 97, 238, 0.05);
    color: var(--primary-color);
}

.nav-links li a i {
    margin-right: 15px;
    font-size: 18px;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

header {
    background-color: var(--card-bg);
    box-shadow: var(--shadow);
    padding: 15px 30px;
    position: sticky;
    top: 0;
    z-index: 99;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.menu-toggle {
    display: none;
    font-size: 20px;
    color: var(--text-color);
}

.search-box {
    display: flex;
    align-items: center;
    background-color: var(--bg-color);
    border-radius: 30px;
    padding: 8px 15px;
    width: 350px;
    position: relative;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.search-box input {
    border: none;
    background: none;
    outline: none;
    width: 100%;
    padding-right: 10px;
    font-size: 14px;
}

.search-box i.fa-search {
    color: var(--text-light);
    margin-right: 5px;
}

.search-clear {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.search-clear:hover {
    opacity: 1;
    color: var(--danger-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-info i {
    font-size: 24px;
    color: var(--primary-color);
}

/* Content Area */
.content {
    padding: 30px;
    flex: 1;
}

/* Dashboard */
.dashboard h1 {
    margin-bottom: 30px;
    font-weight: 600;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h1 {
    font-weight: 600;
}

/* Filters */
.filters-container {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 30px;
}

.filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-divider {
    width: 100%;
    height: 1px;
    background-color: var(--border-color);
    margin: 15px 0;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

.card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.card-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.card-info h3 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-light);
    margin-bottom: 5px;
}

.card-info p {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
}

.chart {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 20px;
}

.chart h3 {
    margin-bottom: 20px;
    font-weight: 500;
}

.chart-placeholder {
    width: 100%;
    overflow: hidden;
    border-radius: 8px;
}

.chart-placeholder img {
    width: 100%;
    height: auto;
}

/* Negative stock table styles */
.negative-stock-container {
    max-height: 300px;
    overflow-y: auto;
}

.negative-stock-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

.negative-stock-table th,
.negative-stock-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.negative-stock-table th {
    background-color: var(--bg-color);
    font-weight: 600;
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 1;
}

.negative-stock-table tbody tr:hover {
    background-color: var(--bg-color);
}

.negative-stock-table .negative-amount {
    color: #e74c3c;
    font-weight: 600;
}

.loading-message,
.empty-message,
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.loading-message i {
    margin-right: 8px;
}

.empty-message {
    color: #27ae60;
}

.empty-message i {
    margin-right: 8px;
    color: #27ae60;
}

.error-message {
    color: #e74c3c;
}

.error-message i {
    margin-right: 8px;
}

/* Material row color coding based on stock levels */
.material-row-negative {
    background-color: #ffebee !important; /* Light red background */
    border-left: 4px solid #f44336; /* Red left border */
}

.material-row-negative:hover {
    background-color: #ffcdd2 !important; /* Darker red on hover */
}

.material-row-critical {
    background-color: #fff8e1 !important; /* Light yellow background */
    border-left: 4px solid #ff9800; /* Orange left border */
}

.material-row-critical:hover {
    background-color: #ffecb3 !important; /* Darker yellow on hover */
}

.material-row-high {
    background-color: #e8f5e8 !important; /* Light green background */
    border-left: 4px solid #4caf50; /* Green left border */
}

.material-row-high:hover {
    background-color: #c8e6c9 !important; /* Darker green on hover */
}

.material-row-negative .negative-amount {
    color: #d32f2f;
    font-weight: 600;
}

.material-row-critical .critical-amount {
    color: #f57c00;
    font-weight: 600;
}

.material-row-high .high-amount {
    color: #388e3c;
    font-weight: 600;
}

/* Recent Activities */
.recent-activities {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.activity:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.activity-icon i {
    font-size: 16px;
    color: var(--primary-color);
}

.activity-details p {
    font-size: 14px;
    margin-bottom: 5px;
}

.activity-details span {
    font-size: 12px;
    color: var(--text-light);
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: inherit;
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.input-with-button {
    display: flex;
}

.input-with-button .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    flex: 1;
}

.input-with-button .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.btn {
    padding: 10px 20px;
    border-radius: 5px;
    font-family: inherit;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #e9ecef;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background-color: rgba(67, 97, 238, 0.05);
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 500;
    color: var(--text-color);
}

tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.02);
}

/* Count Tables Structure */
.count-tables {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tables-row {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin-bottom: 20px;
}

.count-epc-list, .count-epc-summary {
    flex: 1;
    min-width: 0;
}

.count-results, .count-history {
    width: 100%;
}

.count-epc-list h2, .count-epc-summary h2, .count-results h2, .count-history h2 {
    margin-bottom: 15px;
    font-weight: 600;
}

.action-buttons {
    margin-bottom: 15px;
}

/* EPC Table Specific Styles */
#epcListTable .status, #epcSummaryTable .status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

#epcListTable .status.valid, #epcSummaryTable .status.valid {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

#epcListTable .status.invalid, #epcSummaryTable .status.invalid {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

#epcListTable .status.processing, #epcSummaryTable .status.processing {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-weight: 500;
}

.close-modal {
    font-size: 20px;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
}

/* Button Icons */
.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    background-color: transparent;
    transition: var(--transition);
    margin-right: 5px;
}

.btn-icon:hover {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-icon.view:hover {
    color: var(--primary-color);
}

.btn-icon.edit:hover {
    color: var(--accent-color);
}

.btn-icon.delete:hover {
    color: var(--danger-color);
}

.btn-icon.print:hover {
    color: var(--success-color);
}

.btn-icon.approve:hover {
    color: var(--success-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 30px;
}

.page-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin: 0 10px;
    max-width: 500px;
    justify-content: center;
}

.btn-page {
    width: 36px;
    height: 36px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.btn-page.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-page:hover:not(.active):not(:disabled) {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px;
    z-index: 1001;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.info {
    border-left: 4px solid var(--primary-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification-content {
    display: flex;
    align-items: center;
}

.notification-content i {
    font-size: 20px;
    margin-right: 10px;
}

.notification-content i.fa-check-circle {
    color: var(--success-color);
}

.notification-content i.fa-exclamation-circle {
    color: var(--danger-color);
}

.notification-content i.fa-info-circle {
    color: var(--primary-color);
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 14px;
    color: var(--text-light);
    cursor: pointer;
}

/* Status Indicators */
.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.status.pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.status.approved {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.status.completed {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.status.cancelled {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.status.shipped {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.status.draft {
    background-color: rgba(158, 158, 158, 0.1);
    color: #9e9e9e;
}

.status.ordered {
    background-color: rgba(0, 188, 212, 0.1);
    color: #00bcd4;
}

.status.received {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -250px;
        height: 100%;
    }

    .sidebar.active {
        left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .search-box {
        width: 200px;
    }
}

@media (max-width: 576px) {
    .header-content {
        flex-wrap: wrap;
        gap: 10px;
    }

    .search-box {
        order: 3;
        width: 100%;
        margin-top: 10px;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .content {
        padding: 20px;
    }
}

/* Status Cards for Etiketleme */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-info {
    flex: 1;
}

.status-info h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-number {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
}

.status-light {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ef4444;
}

.status-light.connected {
    background-color: #10b981;
}

.status-light.disconnected {
    background-color: #ef4444;
}

/* Main Panel */
.main-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f9fafb;
}

.panel-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.panel-actions {
    display: flex;
    gap: 10px;
}

/* Filters Panel */
.filters-panel {
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.filter-row {
    display: flex;
    align-items: end;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group span {
    align-self: center;
    color: #6b7280;
    font-weight: 500;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-input {
    display: none;
}

.toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: #d1d5db;
    border-radius: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.toggle-label::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.toggle-input:checked + .toggle-label {
    background-color: #3b82f6;
}

.toggle-input:checked + .toggle-label::after {
    transform: translateX(26px);
}

.toggle-text {
    font-size: 14px;
    color: #6b7280;
}

/* Button Sizes */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Data Table */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #374151;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid #e5e7eb;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e5e7eb;
}

.data-table tbody tr:hover {
    background-color: #f8fafc;
}

/* Status badges */
.status.active {
    background-color: #dcfce7;
    color: #166534;
}

.status.inactive {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Status Actions */
.status-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Connection Test */
.connection-test {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.connection-status {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    display: none;
}

.connection-status.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
    display: block;
}

.connection-status.error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
    display: block;
}

.connection-status.testing {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fed7aa;
    display: block;
}

/* Form text */
.form-text {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
    display: block;
}

/* Button states for connection */
.btn.connected {
    background-color: #10b981;
    color: white;
    border-color: #10b981;
}

.btn.connected:hover {
    background-color: #059669;
    border-color: #059669;
}

.btn.disconnected {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
}

.btn.disconnected:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}
