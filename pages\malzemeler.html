<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mal<PERSON>meler - RFID Envanter Y<PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="logo">
                <h2>RFID Sistem</h2>
            </div>
            <ul class="nav-links">
                <li>
                    <a href="../index.html">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </li>
                <li class="active">
                    <a href="malzemeler.html">
                        <i class="fas fa-boxes"></i>
                        <span><PERSON><PERSON><PERSON><PERSON></span>
                    </a>
                </li>
                <li>
                    <a href="sayim.html">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Sayım</span>
                    </a>
                </li>
                <li>
                    <a href="ambar-fisi.html">
                        <i class="fas fa-receipt"></i>
                        <span>Ambar Fişi</span>
                    </a>
                </li>
                <li>
                    <a href="satin-alma.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Satın Alma</span>
                    </a>
                </li>
                <li>
                    <a href="satis.html">
                        <i class="fas fa-cash-register"></i>
                        <span>Satış</span>
                    </a>
                </li>
                <li>
                    <a href="etiketleme.html">
                        <i class="fas fa-tags"></i>
                        <span>Etiketleme</span>
                    </a>
                </li>
                <li>
                    <a href="ayarlar.html">
                        <i class="fas fa-cog"></i>
                        <span>Ayarlar</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header>
                <div class="header-content">
                    <button class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="search-box">
                        <input type="text" placeholder="Malzeme kodu veya adı ile ara...">
                        <i class="fas fa-search"></i>
                        <button class="search-clear" title="Aramayı temizle"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="user-info">
                        <span>Hoş Geldiniz, Kullanıcı</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </header>

            <div class="content">
                <div class="page-header">
                    <h1>Malzemeler</h1>
                    <button class="btn btn-primary" id="addMaterialBtn">
                        <i class="fas fa-plus"></i> Yeni Malzeme Ekle
                    </button>
                </div>

                <div class="filters">
                    <div class="filter-group">
                        <label for="firmNrSelect">Firma No:</label>
                        <select id="firmNrSelect" class="form-control">
                            <option value="">Tümü</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="categoryFilter">Kategori:</label>
                        <select id="categoryFilter" class="form-control">
                            <option value="">Tümü</option>
                            <option value="elektronik">Elektronik</option>
                            <option value="mekanik">Mekanik</option>
                            <option value="hammadde">Hammadde</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="statusFilter">Durum:</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">Tümü</option>
                            <option value="aktif">Aktif</option>
                            <option value="pasif">Pasif</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="stockFilter">Stok Durumu:</label>
                        <select id="stockFilter" class="form-control">
                            <option value="">Tümü</option>
                            <option value="negatif">Negatif</option>
                            <option value="kritik">Kritik</option>
                            <option value="yuksek">Yüksek</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary" id="filterBtn">Filtrele</button>
                </div>

                <div class="table-container">
                    <table id="materialsTable">
                        <thead>
                            <tr>
                                <th>Firma No</th>
                                <th>Malzeme Kodu</th>
                                <th>Malzeme Adı</th>
                                <th>Varyant Kodu</th>
                                <th>Varyant Adı</th>
                                <th>Stok Miktarı</th>
                                <th>Depo</th>
                                <th>Birim</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Table data will be populated dynamically -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="btn-page prev" disabled><i class="fas fa-chevron-left"></i></button>
                    <div class="page-numbers">
                        <!-- Page numbers will be added dynamically -->
                    </div>
                    <button class="btn-page next"><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        </main>
    </div>

    <!-- Material Form Modal -->
    <div class="modal" id="materialModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Yeni Malzeme Ekle</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="materialForm">
                    <div class="form-group">
                        <label for="materialCode">Malzeme Kodu</label>
                        <input type="text" id="materialCode" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="materialName">Malzeme Adı</label>
                        <input type="text" id="materialName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="materialCategory">Kategori</label>
                        <select id="materialCategory" class="form-control" required>
                            <option value="">Seçiniz</option>
                            <option value="elektronik">Elektronik</option>
                            <option value="mekanik">Mekanik</option>
                            <option value="hammadde">Hammadde</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="materialStock">Stok Miktarı</label>
                        <input type="number" id="materialStock" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="materialUnit">Birim</label>
                        <select id="materialUnit" class="form-control" required>
                            <option value="">Seçiniz</option>
                            <option value="adet">Adet</option>
                            <option value="kg">Kg</option>
                            <option value="metre">Metre</option>
                            <option value="litre">Litre</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="materialPrice">Birim Fiyat (₺)</label>
                        <input type="number" id="materialPrice" class="form-control" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="materialStatus">Durum</label>
                        <select id="materialStatus" class="form-control" required>
                            <option value="aktif">Aktif</option>
                            <option value="pasif">Pasif</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="materialDescription">Açıklama</label>
                        <textarea id="materialDescription" class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelMaterialBtn">İptal</button>
                <button class="btn btn-primary" id="saveMaterialBtn">Kaydet</button>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script src="../js/pages/malzemeler.js"></script>
</body>
</html>
