from flask import Flask, jsonify, request, send_from_directory
import requests
import time
import os
import sys
import logging
import json
import decimal
from flask_cors import CORS

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import db_connection

# Global variable to track if RFID reading is active
rfid_reading_active = False

# Custom JSON encoder to handle Decimal and datetime types
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        elif hasattr(obj, 'isoformat'):  # Handle datetime objects
            return obj.isoformat()
        return super(CustomJSONEncoder, self).default(obj)

app = Flask(__name__, static_folder='..', static_url_path='')
# Set the custom JSON encoder
app.json_encoder = CustomJSONEncoder
# Enable CORS with more specific configuration
CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)

@app.route('/')
def index():
    """Root endpoint to verify the API is running"""
    return jsonify({
        'status': 'success',
        'message': 'RFID Inventory Management System API is running',
        'endpoints': {
            '/api/db/test-connection': 'Test database connection',
            '/api/db/save-config': 'Save database configuration',
            '/api/materials': 'Get materials using stored procedure',
            '/api/warehouses': 'Get warehouses using stored procedure',
            '/api/inventory-counts': 'Get inventory count history',
            '/api/inventory-counts/start': 'Start a new inventory count',
            '/api/inventory-counts/materials': 'Get materials for a specific location',
            '/api/inventory-counts/items': 'Save an inventory count item',
            '/api/inventory-counts/complete': 'Complete an inventory count',
            '/api/inventory-counts/epc-summary': 'Get summary of onhand inventory by EPC list',
            '/api/epc-tags': 'Get EPC tags from the database',
            '/api/bb-101-onhand-count': 'Get row count from BB_101_ONHAND table',
            '/api/negative-stock': 'Get items with negative stock from BB_101_ONHAND table',
            '/api/critical-stock-count': 'Get count of items with critical stock (ONHAND < 100)',
            '/api/firm-numbers': 'Get distinct FIRMNR values from BB_101_ONHAND table',
            '/api/all-materials': 'Get all materials from BB_101_ONHAND table',
            '/api/rfid/settings': 'Get or save RFID settings',
            '/api/rfid/test-connection': 'Test connection to RFID antenna',
            '/api/rfid/stop-reading': 'Stop RFID reading process',
            '/api/rfid/scan-devices': 'Scan for RFID devices',
            '/read-tag': 'Read RFID tags'
        }
    })

@app.route('/api/rfid/settings', methods=['GET', 'POST'])
def rfid_settings():
    """Get or save RFID settings"""
    try:
        if request.method == 'POST':
            # Save settings
            settings = request.json
            # In a real application, you would save these settings to a database or config file
            # For now, we'll just return success
            return jsonify({
                'success': True,
                'message': 'RFID ayarları kaydedildi.'
            })
        else:
            # Get settings
            # In a real application, you would load these settings from a database or config file
            # For now, we'll just return default settings
            return jsonify({
                'success': True,
                'data': {
                    'ipAddress': '*************',
                    'port': 8080,
                    'power': 30,
                    'mode': 'command',
                    'timeout': 300,
                    'sessionId': 1
                }
            })
    except Exception as e:
        logger.exception(f"Error in rfid_settings endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/rfid/test-connection', methods=['GET'])
def test_rfid_connection():
    """Test connection to RFID antenna"""
    try:
        # Get RFID settings from query parameters or use defaults
        ip_address = request.args.get('ip', '*************')
        port = request.args.get('port', '8080')
        timeout = int(request.args.get('timeout', '300'))

        base_url = f"http://{ip_address}:{port}"
        logger.info(f"Testing RFID connection to {base_url}")

        try:
            # Try to connect to the RFID antenna
            response = requests.get(base_url, timeout=5)

            if response.status_code == 200:
                # If we can connect, try to read tags
                result = read_tags_internal(ip_address, port, timeout)

                if result.get('tagList') and len(result.get('tagList')) > 0:
                    tag_count = len(result.get('tagList'))
                    return jsonify({
                        'success': True,
                        'message': f'{tag_count} etiket okundu.',
                        'data': result
                    })
                else:
                    return jsonify({
                        'success': True,
                        'message': 'Bağlantı başarılı fakat etiket okunamadı.',
                        'data': result
                    })
            else:
                return jsonify({
                    'success': False,
                    'message': f'Bağlantı başarısız: HTTP {response.status_code}'
                })
        except requests.exceptions.RequestException as e:
            logger.exception(f"Error connecting to RFID antenna: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Bağlantı hatası: {str(e)}'
            })
    except Exception as e:
        logger.exception(f"Error in test_rfid_connection endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/rfid/stop-reading', methods=['GET'])
def stop_rfid_reading():
    """Stop RFID reading process"""
    global rfid_reading_active

    try:
        # Get RFID settings from query parameters or use defaults
        ip_address = request.args.get('ip', '*************')
        port = request.args.get('port', '8080')

        base_url = f"http://{ip_address}:{port}"
        logger.info(f"Stopping RFID reading at {base_url}")

        headers = {
            "Connection": "keep-alive",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": base_url,
            "Referer": f"{base_url}/uhf_inventory.html",
            "Accept-Language": "en-US,en;q=0.9"
        }

        # Send stop inventory request
        stop_payload = {
            "data": "{\"type\":\"Reader-stopInventoryRequest\"}"
        }
        stop_url = f"{base_url}/InventoryController/stopInventoryRequest"

        try:
            stop_response = requests.post(stop_url, data=stop_payload, headers=headers, timeout=5)
            logger.info(f"StopInventory: {stop_response.status_code}")

            # Set the global flag to indicate that RFID reading is stopped
            rfid_reading_active = False
            logger.info("RFID reading has been explicitly stopped")

            return jsonify({
                'success': True,
                'message': 'RFID okuma durduruldu.'
            })
        except requests.exceptions.RequestException as e:
            logger.exception(f"Error stopping RFID reading: {str(e)}")

            # Even if there's an error communicating with the device,
            # we should still set the flag to stop reading
            rfid_reading_active = False
            logger.info("RFID reading has been stopped due to error")

            return jsonify({
                'success': False,
                'message': f'RFID okuma durdurma hatası: {str(e)}'
            })
    except Exception as e:
        logger.exception(f"Error in stop_rfid_reading endpoint: {str(e)}")

        # Even if there's an exception, we should still set the flag to stop reading
        rfid_reading_active = False
        logger.info("RFID reading has been stopped due to exception")

        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/rfid/scan-devices', methods=['GET'])
def scan_rfid_devices():
    """Scan for RFID devices"""
    try:
        # Get RFID settings from query parameters or use defaults
        ip_address = request.args.get('ip', '*************')
        port = request.args.get('port', '8080')

        base_url = f"http://{ip_address}:{port}"
        logger.info(f"Scanning for RFID devices at {base_url}")

        try:
            # Try to connect to the RFID antenna
            response = requests.get(base_url, timeout=5)

            if response.status_code == 200:
                # If we can connect, return success
                return jsonify({
                    'success': True,
                    'message': 'RFID cihazı bulundu.',
                    'devices': [{
                        'ip': ip_address,
                        'port': port,
                        'name': 'RFID Reader',
                        'status': 'online'
                    }]
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'Bağlantı başarısız: HTTP {response.status_code}'
                })
        except requests.exceptions.RequestException as e:
            logger.exception(f"Error scanning for RFID devices: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Tarama hatası: {str(e)}'
            })
    except Exception as e:
        logger.exception(f"Error in scan_rfid_devices endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

def read_tags_internal(ip_address, port, timeout):
    """Internal function to read RFID tags"""
    global rfid_reading_active

    try:
        base_url = f"http://{ip_address}:{port}"
        logger.info(f"Reading RFID tags from {base_url}")

        headers = {
            "Connection": "keep-alive",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": base_url,
            "Referer": f"{base_url}/uhf_inventory.html",
            "Accept-Language": "en-US,en;q=0.9"
        }

        # If RFID reading is not already active, start it
        if not rfid_reading_active:
            # 1. Envanter taramasını başlat
            start_payload = {
                "data": "{\"type\":\"Reader-startInventoryRequest\",\"tagFilter\":{\"tagMemoryBank\":\"epc\",\"bitOffset\":0,\"bitLength\":0,\"hexAsk\":null}}"
            }
            start_url = f"{base_url}/InventoryController/startInventoryRequest"
            start_response = requests.post(start_url, data=start_payload, headers=headers, timeout=5)
            logger.info(f"StartInventory: {start_response.status_code}")

            # Set the global flag to indicate that RFID reading is active
            rfid_reading_active = True
            logger.info("RFID reading started and will continue until explicitly stopped")
        else:
            logger.info("RFID reading is already active, continuing to read tags")

        # Bekleme süresini kaldırdık - maksimum hız için
        # Hiç bekleme yapmadan doğrudan etiketleri okuyoruz

        # 3. Tag'leri çek
        read_url = f"{base_url}/InventoryController/tagReportingDataAndIndex"
        read_response = requests.post(read_url, data="", headers=headers, timeout=5)

        logger.info(f"Gelen cevap: {read_response.text}")

        if read_response.status_code == 200:
            try:
                # Parse the JSON response
                response_data = read_response.json()

                # Log the number of tags found
                if response_data and 'data' in response_data and isinstance(response_data['data'], list):
                    logger.info(f"Found {len(response_data['data'])} tags")
                else:
                    logger.warning("No tags found in response data")

                return response_data
            except Exception as e:
                logger.error(f"Error parsing JSON response: {str(e)}")
                return {
                    "warning": "JSON parse failed",
                    "raw": read_response.text
                }
        else:
            return {
                "error": "Unexpected response",
                "status_code": read_response.status_code,
                "raw": read_response.text
            }

    except Exception as e:
        logger.exception(f"Error reading RFID tags: {str(e)}")
        return {'error': str(e)}

    # Note: We no longer reset rfid_reading_active here, so it will continue
    # until explicitly stopped by the stop_rfid_reading endpoint

@app.route('/read-tag', methods=['GET'])
def read_tags():
    try:
        # Get RFID settings from query parameters or use defaults
        ip_address = request.args.get('ip', '*************')
        port = request.args.get('port', '8080')
        timeout = int(request.args.get('timeout', '300'))

        # Read tags using the internal function
        result = read_tags_internal(ip_address, port, timeout)

        # Return the result as JSON
        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error reading RFID tags: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Database connection routes
@app.route('/api/db/test-connection', methods=['POST'])
def test_db_connection():
    try:
        config = request.json
        success, message = db_connection.test_connection(config)

        return jsonify({
            'success': success,
            'message': message
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/db/save-config', methods=['POST'])
def save_db_config():
    try:
        config = request.json
        db_connection.save_db_config(config)

        return jsonify({
            'success': True,
            'message': 'Veritabanı ayarları başarıyla kaydedildi.'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/materials', methods=['GET'])
def get_materials():
    try:
        firmnr = request.args.get('firmnr', '')
        invenno = request.args.get('invenno', '')

        if not firmnr:
            logger.warning("Missing required parameter: firmnr")
            return jsonify({
                'success': False,
                'message': 'FIRMNR parametresi gereklidir.'
            }), 400

        logger.info(f"Getting materials with FIRMNR={firmnr}, INVENNO={invenno}")
        success, result = db_connection.get_materials(firmnr, invenno)

        if success:
            logger.info(f"Successfully retrieved {len(result)} materials")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve materials: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_materials endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/warehouses', methods=['GET'])
def get_warehouses():
    try:
        logger.info("Getting warehouses")
        success, result = db_connection.get_warehouses()

        if success:
            logger.info(f"Successfully retrieved {len(result)} warehouses")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve warehouses: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_warehouses endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/inventory-counts', methods=['GET'])
def get_inventory_counts():
    try:
        logger.info("Getting inventory count history")
        success, result = db_connection.get_inventory_counts()

        if success:
            logger.info(f"Successfully retrieved {len(result)} inventory counts")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve inventory counts: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_inventory_counts endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/inventory-counts/start', methods=['POST'])
def start_inventory_count():
    try:
        data = request.json
        location = data.get('location')
        count_type = data.get('type')
        description = data.get('description', '')
        category = data.get('category', None)

        if not location:
            logger.warning("Missing required parameter: location")
            return jsonify({
                'success': False,
                'message': 'Sayım lokasyonu gereklidir.'
            }), 400

        if not count_type:
            logger.warning("Missing required parameter: type")
            return jsonify({
                'success': False,
                'message': 'Sayım tipi gereklidir.'
            }), 400

        logger.info(f"Starting inventory count with location={location}, type={count_type}")
        success, result = db_connection.start_inventory_count(location, count_type, description, category)

        if success:
            logger.info(f"Successfully started inventory count: {result}")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to start inventory count: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in start_inventory_count endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/inventory-counts/materials', methods=['GET'])
def get_materials_by_location():
    try:
        location = request.args.get('location', '')

        if not location:
            logger.warning("Missing required parameter: location")
            return jsonify({
                'success': False,
                'message': 'Lokasyon parametresi gereklidir.'
            }), 400

        logger.info(f"Getting materials for location={location}")
        success, result = db_connection.get_materials_by_location(location)

        if success:
            logger.info(f"Successfully retrieved {len(result)} materials for location {location}")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve materials: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_materials_by_location endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/inventory-counts/items', methods=['POST'])
def save_inventory_count_item():
    try:
        data = request.json
        count_id = data.get('count_id')
        epc = data.get('epc')
        item_code = data.get('item_code')
        system_qty = data.get('system_qty', 0)
        count_qty = data.get('count_qty', 1)  # Default to 1 for RFID scans

        if not count_id:
            logger.warning("Missing required parameter: count_id")
            return jsonify({
                'success': False,
                'message': 'Sayım ID gereklidir.'
            }), 400

        if not epc:
            logger.warning("Missing required parameter: epc")
            return jsonify({
                'success': False,
                'message': 'EPC değeri gereklidir.'
            }), 400

        if not item_code:
            logger.warning("Missing required parameter: item_code")
            return jsonify({
                'success': False,
                'message': 'Malzeme kodu gereklidir.'
            }), 400

        logger.info(f"Saving inventory count item with count_id={count_id}, item_code={item_code}")
        success, result = db_connection.save_inventory_count_item(count_id, epc, item_code, system_qty, count_qty)

        if success:
            logger.info(f"Successfully saved inventory count item: {result}")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to save inventory count item: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in save_inventory_count_item endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/inventory-counts/complete', methods=['POST'])
def complete_inventory_count():
    try:
        data = request.json
        count_id = data.get('count_id')
        item_count = data.get('item_count', 0)

        if not count_id:
            logger.warning("Missing required parameter: count_id")
            return jsonify({
                'success': False,
                'message': 'Sayım ID gereklidir.'
            }), 400

        logger.info(f"Completing inventory count with count_id={count_id}")

        # Update the count of items in the BB_501_INVCNT_ITEMS table
        # This is done by counting the number of items with the given count_id
        # We don't need to pass the item_count parameter to the complete_inventory_count function
        success, result = db_connection.complete_inventory_count(count_id, item_count)

        if success:
            logger.info(f"Successfully completed inventory count: {result}")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to complete inventory count: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in complete_inventory_count endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/inventory-counts/epc-summary', methods=['POST'])
def get_epc_summary():
    try:
        data = request.json
        epc_list = data.get('epc_list', [])
        invenno = data.get('invenno')

        if not epc_list or len(epc_list) == 0:
            logger.warning("Missing required parameter: epc_list")
            return jsonify({
                'success': False,
                'message': 'EPC listesi gereklidir.'
            }), 400

        if not invenno:
            logger.warning("Missing required parameter: invenno")
            return jsonify({
                'success': False,
                'message': 'INVENNO parametresi gereklidir.'
            }), 400

        logger.info(f"Getting EPC summary for {len(epc_list)} EPCs with INVENNO={invenno}")
        success, result = db_connection.get_onhand_by_epc_sum(epc_list, invenno)

        if success:
            logger.info(f"Successfully retrieved EPC summary: {len(result)} items found")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to get EPC summary: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_epc_summary endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/epc-tags', methods=['GET'])
def get_epc_tags():
    """Get EPC tags from the database"""
    try:
        logger.info("Getting EPC tags")
        success, result = db_connection.get_epc_tags()

        if success:
            logger.info(f"Successfully retrieved {len(result)} EPC tags")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve EPC tags: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_epc_tags endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/epc-tags1', methods=['GET'])
def get_epc_tags1():
    """Get EPC tags from the database (alias for /api/epc-tags)"""
    return get_epc_tags()

@app.route('/api/bb-101-onhand-count', methods=['GET'])
def get_bb_101_onhand_count():
    """Get the total row count from BB_101_ONHAND table"""
    try:
        logger.info("Getting BB_101_ONHAND table row count")
        success, result = db_connection.get_bb_101_onhand_count()

        if success:
            logger.info(f"Successfully retrieved BB_101_ONHAND count: {result}")
            return jsonify({
                'success': True,
                'count': result
            })
        else:
            logger.error(f"Failed to retrieve BB_101_ONHAND count: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_bb_101_onhand_count endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/negative-stock', methods=['GET'])
def get_negative_stock():
    """Get items with negative stock from BB_101_ONHAND table"""
    try:
        logger.info("Getting items with negative stock")
        success, result = db_connection.get_negative_stock_items()

        if success:
            logger.info(f"Successfully retrieved {len(result)} items with negative stock")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve negative stock items: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_negative_stock endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/critical-stock-count', methods=['GET'])
def get_critical_stock_count():
    """Get the count of items with critical stock (ONHAND < 100) from BB_101_ONHAND table"""
    try:
        logger.info("Getting critical stock count")
        success, result = db_connection.get_critical_stock_count()

        if success:
            logger.info(f"Successfully retrieved critical stock count: {result}")
            return jsonify({
                'success': True,
                'count': result
            })
        else:
            logger.error(f"Failed to retrieve critical stock count: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_critical_stock_count endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/firm-numbers', methods=['GET'])
def get_firm_numbers():
    """Get distinct FIRMNR values from BB_101_ONHAND table"""
    try:
        logger.info("Getting firm numbers")
        success, result = db_connection.get_firm_numbers()

        if success:
            logger.info(f"Successfully retrieved {len(result)} firm numbers")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve firm numbers: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_firm_numbers endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

@app.route('/api/all-materials', methods=['GET'])
def get_all_materials():
    """Get all materials from BB_101_ONHAND table"""
    try:
        logger.info("Getting all materials")
        success, result = db_connection.get_all_materials()

        if success:
            logger.info(f"Successfully retrieved {len(result)} materials")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            logger.error(f"Failed to retrieve materials: {result}")
            return jsonify({
                'success': False,
                'message': result
            }), 500
    except Exception as e:
        logger.exception(f"Error in get_all_materials endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Hata: {str(e)}'
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
