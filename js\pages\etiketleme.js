document.addEventListener('DOMContentLoaded', function() {
    // Device connection settings
    let printerSettings = {
        ip: '*************',
        port: 9100,
        timeout: 5,
        connected: false
    };

    let readerSettings = {
        ip: '*************',
        port: 8080,
        timeout: 5,
        connected: false
    };

    // Load settings from localStorage
    const savedPrinterSettings = localStorage.getItem('printerSettings');
    const savedReaderSettings = localStorage.getItem('readerSettings');

    if (savedPrinterSettings) {
        printerSettings = { ...printerSettings, ...JSON.parse(savedPrinterSettings) };
    }

    if (savedReaderSettings) {
        readerSettings = { ...readerSettings, ...JSON.parse(savedReaderSettings) };
    }

    // DOM elements
    const connectPrinterBtn = document.getElementById('connectPrinterBtn');
    const connectReaderBtn = document.getElementById('connectReaderBtn');
    const printerSettingsBtn = document.getElementById('printerSettingsBtn');
    const readerSettingsBtn = document.getElementById('readerSettingsBtn');

    // Printer connection
    connectPrinterBtn.addEventListener('click', function() {
        if (printerSettings.connected) {
            disconnectPrinter();
        } else {
            connectToPrinter();
        }
    });

    function connectToPrinter() {
        connectPrinterBtn.disabled = true;
        connectPrinterBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';

        // Simulate connection attempt
        setTimeout(() => {
            const success = Math.random() > 0.3; // 70% success rate for demo

            if (success) {
                printerSettings.connected = true;
                updatePrinterStatus(true, `Bağlı: ${printerSettings.ip}:${printerSettings.port}`);
                connectPrinterBtn.innerHTML = '<i class="fas fa-unlink"></i> Bağlantıyı Kes';
                connectPrinterBtn.classList.remove('btn-secondary');
                connectPrinterBtn.classList.add('connected');
                showNotification('RFID yazıcı bağlantısı kuruldu.', 'success');
            } else {
                printerSettings.connected = false;
                updatePrinterStatus(false, 'Bağlantı Hatası');
                connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
                showNotification('RFID yazıcı bağlantısı kurulamadı.', 'error');
            }

            connectPrinterBtn.disabled = false;
        }, 2000);
    }

    function disconnectPrinter() {
        printerSettings.connected = false;
        updatePrinterStatus(false, 'Bağlı Değil');
        connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
        connectPrinterBtn.classList.remove('connected');
        connectPrinterBtn.classList.add('btn-secondary');
        showNotification('RFID yazıcı bağlantısı kesildi.', 'info');
    }

    // Reader connection
    connectReaderBtn.addEventListener('click', function() {
        if (readerSettings.connected) {
            disconnectReader();
        } else {
            connectToReader();
        }
    });

    function connectToReader() {
        connectReaderBtn.disabled = true;
        connectReaderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';

        // Simulate connection attempt
        setTimeout(() => {
            const success = Math.random() > 0.3; // 70% success rate for demo

            if (success) {
                readerSettings.connected = true;
                updateReaderStatus(true, `Bağlı: ${readerSettings.ip}:${readerSettings.port}`);
                connectReaderBtn.innerHTML = '<i class="fas fa-unlink"></i> Bağlantıyı Kes';
                connectReaderBtn.classList.remove('btn-secondary');
                connectReaderBtn.classList.add('connected');
                showNotification('RFID okuyucu bağlantısı kuruldu.', 'success');
            } else {
                readerSettings.connected = false;
                updateReaderStatus(false, 'Bağlantı Hatası');
                connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
                showNotification('RFID okuyucu bağlantısı kurulamadı.', 'error');
            }

            connectReaderBtn.disabled = false;
        }, 2000);
    }

    function disconnectReader() {
        readerSettings.connected = false;
        updateReaderStatus(false, 'Bağlı Değil');
        connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
        connectReaderBtn.classList.remove('connected');
        connectReaderBtn.classList.add('btn-secondary');
        showNotification('RFID okuyucu bağlantısı kesildi.', 'info');
    }

    // Status update functions
    function updatePrinterStatus(connected, statusText) {
        const statusLight = document.getElementById('printerStatus');
        const statusTextElement = document.getElementById('printerStatusText');

        if (connected) {
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
        } else {
            statusLight.classList.remove('connected');
            statusLight.classList.add('disconnected');
        }

        statusTextElement.textContent = statusText;
    }

    function updateReaderStatus(connected, statusText) {
        const statusLight = document.getElementById('readerStatus');
        const statusTextElement = document.getElementById('readerStatusText');

        if (connected) {
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
        } else {
            statusLight.classList.remove('connected');
            statusLight.classList.add('disconnected');
        }

        statusTextElement.textContent = statusText;
    }

    // Settings modal functionality
    const printerSettingsModal = document.getElementById('printerSettingsModal');
    const readerSettingsModal = document.getElementById('readerSettingsModal');

    // Printer settings
    printerSettingsBtn.addEventListener('click', function() {
        openPrinterSettings();
    });

    function openPrinterSettings() {
        // Populate form with current settings
        document.getElementById('printerIP').value = printerSettings.ip;
        document.getElementById('printerPort').value = printerSettings.port;
        document.getElementById('printerTimeout').value = printerSettings.timeout;

        // Clear connection status
        document.getElementById('printerConnectionStatus').style.display = 'none';

        printerSettingsModal.style.display = 'flex';
    }

    // Reader settings
    readerSettingsBtn.addEventListener('click', function() {
        openReaderSettings();
    });

    function openReaderSettings() {
        // Populate form with current settings
        document.getElementById('readerIP').value = readerSettings.ip;
        document.getElementById('readerPort').value = readerSettings.port;
        document.getElementById('readerTimeout').value = readerSettings.timeout;

        // Clear connection status
        document.getElementById('readerConnectionStatus').style.display = 'none';

        readerSettingsModal.style.display = 'flex';
    }

    // Test connections
    document.getElementById('testPrinterConnectionBtn').addEventListener('click', function() {
        testPrinterConnection();
    });

    document.getElementById('testReaderConnectionBtn').addEventListener('click', function() {
        testReaderConnection();
    });

    function testPrinterConnection() {
        const ip = document.getElementById('printerIP').value;
        const port = document.getElementById('printerPort').value;
        const statusDiv = document.getElementById('printerConnectionStatus');
        const testBtn = document.getElementById('testPrinterConnectionBtn');

        if (!ip || !port) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = 'IP adresi ve port gerekli!';
            return;
        }

        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test ediliyor...';
        statusDiv.className = 'connection-status testing';
        statusDiv.textContent = `${ip}:${port} adresine bağlanılıyor...`;

        // Simulate connection test
        setTimeout(() => {
            const success = Math.random() > 0.4; // 60% success rate

            if (success) {
                statusDiv.className = 'connection-status success';
                statusDiv.textContent = `✓ Bağlantı başarılı! (${ip}:${port})`;
            } else {
                statusDiv.className = 'connection-status error';
                statusDiv.textContent = `✗ Bağlantı başarısız! (${ip}:${port})`;
            }

            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
        }, 2000);
    }

    function testReaderConnection() {
        const ip = document.getElementById('readerIP').value;
        const port = document.getElementById('readerPort').value;
        const statusDiv = document.getElementById('readerConnectionStatus');
        const testBtn = document.getElementById('testReaderConnectionBtn');

        if (!ip || !port) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = 'IP adresi ve port gerekli!';
            return;
        }

        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test ediliyor...';
        statusDiv.className = 'connection-status testing';
        statusDiv.textContent = `${ip}:${port} adresine bağlanılıyor...`;

        // Simulate connection test
        setTimeout(() => {
            const success = Math.random() > 0.4; // 60% success rate

            if (success) {
                statusDiv.className = 'connection-status success';
                statusDiv.textContent = `✓ Bağlantı başarılı! (${ip}:${port})`;
            } else {
                statusDiv.className = 'connection-status error';
                statusDiv.textContent = `✗ Bağlantı başarısız! (${ip}:${port})`;
            }

            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
        }, 2000);
    }

    // Save settings
    document.getElementById('savePrinterSettingsBtn').addEventListener('click', function() {
        savePrinterSettings();
    });

    document.getElementById('saveReaderSettingsBtn').addEventListener('click', function() {
        saveReaderSettings();
    });

    function savePrinterSettings() {
        const ip = document.getElementById('printerIP').value;
        const port = parseInt(document.getElementById('printerPort').value);
        const timeout = parseInt(document.getElementById('printerTimeout').value);

        if (!ip || !port || !timeout) {
            showNotification('Lütfen tüm alanları doldurun!', 'error');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showNotification('Geçersiz IP adresi formatı!', 'error');
            return;
        }

        // Update settings
        printerSettings.ip = ip;
        printerSettings.port = port;
        printerSettings.timeout = timeout;

        // Save to localStorage
        localStorage.setItem('printerSettings', JSON.stringify(printerSettings));

        // Close modal
        printerSettingsModal.style.display = 'none';

        // Disconnect if connected (settings changed)
        if (printerSettings.connected) {
            disconnectPrinter();
        }

        showNotification('Yazıcı ayarları kaydedildi!', 'success');
    }

    function saveReaderSettings() {
        const ip = document.getElementById('readerIP').value;
        const port = parseInt(document.getElementById('readerPort').value);
        const timeout = parseInt(document.getElementById('readerTimeout').value);

        if (!ip || !port || !timeout) {
            showNotification('Lütfen tüm alanları doldurun!', 'error');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showNotification('Geçersiz IP adresi formatı!', 'error');
            return;
        }

        // Update settings
        readerSettings.ip = ip;
        readerSettings.port = port;
        readerSettings.timeout = timeout;

        // Save to localStorage
        localStorage.setItem('readerSettings', JSON.stringify(readerSettings));

        // Close modal
        readerSettingsModal.style.display = 'none';

        // Disconnect if connected (settings changed)
        if (readerSettings.connected) {
            disconnectReader();
        }

        showNotification('Okuyucu ayarları kaydedildi!', 'success');
    }

    // Modal close functionality
    document.querySelectorAll('.close-modal').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });

    document.getElementById('cancelPrinterSettingsBtn').addEventListener('click', function() {
        printerSettingsModal.style.display = 'none';
    });

    document.getElementById('cancelReaderSettingsBtn').addEventListener('click', function() {
        readerSettingsModal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });
        connectReaderBtn.disabled = true;
        connectReaderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';
        
        // Simulate connection
        setTimeout(() => {
            // Update connection status
            const statusLight = document.querySelector('.device-status:last-child .status-light');
            const statusText = document.querySelector('.device-status:last-child .status-indicator span');
            
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
            statusText.textContent = 'Bağlı: RFID Reader';
            
            // Reset button state
            connectReaderBtn.disabled = false;
            connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Okuyucuya Bağlan';
            
            // Show notification
            showNotification('RFID okuyucu bağlantısı kuruldu.', 'success');
        }, 2000);
    });
    
    // New Tag Modal
    const newTagBtn = document.getElementById('newTagBtn');
    const tagModal = document.getElementById('tagModal');
    const closeModalBtns = document.querySelectorAll('.close-modal');
    const cancelTagBtn = document.getElementById('cancelTagBtn');
    const saveTagBtn = document.getElementById('saveTagBtn');
    
    // Open tag modal
    newTagBtn.addEventListener('click', function() {
        // Reset form
        document.getElementById('tagForm').reset();
        
        // Set default values
        document.getElementById('tagStatus').checked = true;
        document.getElementById('printAfterSave').checked = true;
        
        // Update modal title
        document.querySelector('#tagModal .modal-header h3').textContent = 'Yeni Etiket';
        
        // Show modal
        tagModal.style.display = 'flex';
    });
    
    // Close modals
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            tagModal.style.display = 'none';
            batchTagModal.style.display = 'none';
            printTagsModal.style.display = 'none';
        });
    });
    
    cancelTagBtn.addEventListener('click', function() {
        tagModal.style.display = 'none';
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === tagModal) {
            tagModal.style.display = 'none';
        } else if (event.target === batchTagModal) {
            batchTagModal.style.display = 'none';
        } else if (event.target === printTagsModal) {
            printTagsModal.style.display = 'none';
        }
    });
    
    // Generate EPC
    const generateEPCBtn = document.getElementById('generateEPCBtn');
    generateEPCBtn.addEventListener('click', function() {
        // Generate a random EPC
        const epc = generateRandomEPC();
        document.getElementById('tagEPC').value = epc;
    });
    
    // Function to generate a random EPC
    function generateRandomEPC() {
        const chars = '0123456789ABCDEF';
        let epc = 'E28011606000';
        
        // Add 12 random hex characters
        for (let i = 0; i < 12; i++) {
            epc += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return epc;
    }
    
    // Read EPC from RFID reader
    const readEPCBtn = document.getElementById('readEPCBtn');
    readEPCBtn.addEventListener('click', function() {
        // Check if reader is connected
        const readerStatus = document.querySelector('.device-status:last-child .status-light');
        if (!readerStatus.classList.contains('connected')) {
            showNotification('RFID okuyucu bağlı değil.', 'error');
            return;
        }
        
        // Show loading state
        readEPCBtn.disabled = true;
        readEPCBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Simulate reading
        setTimeout(() => {
            // Generate a random EPC
            const epc = generateRandomEPC();
            document.getElementById('tagEPC').value = epc;
            
            // Reset button state
            readEPCBtn.disabled = false;
            readEPCBtn.innerHTML = '<i class="fas fa-wifi"></i>';
            
            // Show notification
            showNotification('EPC okundu.', 'success');
        }, 1500);
    });
    
    // Search for item
    const searchItemBtn = document.getElementById('searchItemBtn');
    searchItemBtn.addEventListener('click', function() {
        const tagType = document.getElementById('tagType').value;
        
        if (!tagType) {
            showNotification('Lütfen önce etiket tipini seçin.', 'error');
            return;
        }
        
        // Show loading state
        searchItemBtn.disabled = true;
        searchItemBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Simulate search
        setTimeout(() => {
            // Reset button state
            searchItemBtn.disabled = false;
            searchItemBtn.innerHTML = '<i class="fas fa-search"></i>';
            
            // Open a search dialog or dropdown (simplified for demo)
            let itemCode = '';
            let itemName = '';
            
            if (tagType === 'malzeme') {
                itemCode = 'M001';
                itemName = 'Arduino Nano';
            } else if (tagType === 'lokasyon') {
                itemCode = 'LOC001';
                itemName = 'Ana Depo Raf A1';
            } else if (tagType === 'palet') {
                itemCode = 'PAL001';
                itemName = 'Plastik Palet 1';
            }
            
            document.getElementById('tagItem').value = `${itemCode} - ${itemName}`;
        }, 1000);
    });
    
    // Save tag
    saveTagBtn.addEventListener('click', function() {
        const form = document.getElementById('tagForm');
        if (form.checkValidity()) {
            const tagData = {
                type: document.getElementById('tagType').value,
                epc: document.getElementById('tagEPC').value,
                item: document.getElementById('tagItem').value,
                template: document.getElementById('tagTemplate').value,
                description: document.getElementById('tagDescription').value,
                status: document.getElementById('tagStatus').checked ? 'active' : 'inactive',
                printAfterSave: document.getElementById('printAfterSave').checked
            };
            
            // In a real application, you would send this data to a server
            console.log('Tag data:', tagData);
            
            // Close modal
            tagModal.style.display = 'none';
            
            // Show success notification
            showNotification('Etiket başarıyla kaydedildi.', 'success');
            
            // If print after save is checked, show print notification
            if (tagData.printAfterSave) {
                setTimeout(() => {
                    showNotification('Etiket yazdırılıyor...', 'info');
                }, 1000);
            }
        } else {
            form.reportValidity();
        }
    });
    
    // Batch Tagging Modal
    const batchTagBtn = document.getElementById('batchTagBtn');
    const batchTagModal = document.getElementById('batchTagModal');
    const cancelBatchTagBtn = document.getElementById('cancelBatchTagBtn');
    const saveBatchTagBtn = document.getElementById('saveBatchTagBtn');
    const addBatchItemsBtn = document.getElementById('addBatchItemsBtn');
    const importBatchItemsBtn = document.getElementById('importBatchItemsBtn');
    const batchItemsTable = document.getElementById('batchItemsTable').querySelector('tbody');
    
    // Open batch tag modal
    batchTagBtn.addEventListener('click', function() {
        // Reset form
        document.getElementById('batchTagForm').reset();
        
        // Clear items table
        batchItemsTable.innerHTML = '';
        
        // Set default values
        document.getElementById('batchPrintAfterSave').checked = true;
        
        // Show modal
        batchTagModal.style.display = 'flex';
    });
    
    cancelBatchTagBtn.addEventListener('click', function() {
        batchTagModal.style.display = 'none';
    });
    
    // Add batch items
    addBatchItemsBtn.addEventListener('click', function() {
        const tagType = document.getElementById('batchTagType').value;
        
        if (!tagType) {
            showNotification('Lütfen önce etiket tipini seçin.', 'error');
            return;
        }
        
        // Simulate adding items (in a real app, this would open a search dialog)
        let items = [];
        
        if (tagType === 'malzeme') {
            items = [
                { code: 'M001', name: 'Arduino Nano' },
                { code: 'M002', name: 'Motor Sürücü' },
                { code: 'M003', name: 'Alüminyum Profil 20x20' }
            ];
        } else if (tagType === 'lokasyon') {
            items = [
                { code: 'LOC001', name: 'Ana Depo Raf A1' },
                { code: 'LOC002', name: 'Ana Depo Raf A2' },
                { code: 'LOC003', name: 'Üretim Depo Raf B1' }
            ];
        } else if (tagType === 'palet') {
            items = [
                { code: 'PAL001', name: 'Plastik Palet 1' },
                { code: 'PAL002', name: 'Plastik Palet 2' },
                { code: 'PAL003', name: 'Ahşap Palet 1' }
            ];
        }
        
        // Add items to table
        items.forEach(item => {
            addBatchItem(item.code, item.name);
        });
        
        showNotification(`${items.length} öğe eklendi.`, 'success');
    });
    
    // Import batch items
    importBatchItemsBtn.addEventListener('click', function() {
        // In a real application, this would open a file dialog
        showNotification('Bu özellik demo sürümünde kullanılamıyor.', 'info');
    });
    
    // Add batch item to table
    function addBatchItem(code, name) {
        const row = document.createElement('tr');
        
        const codeCell = document.createElement('td');
        codeCell.textContent = code;
        
        const nameCell = document.createElement('td');
        nameCell.textContent = name;
        
        const epcCell = document.createElement('td');
        const epcInput = document.createElement('input');
        epcInput.type = 'text';
        epcInput.className = 'form-control';
        epcInput.value = generateRandomEPC();
        epcCell.appendChild(epcInput);
        
        const actionCell = document.createElement('td');
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn-icon delete';
        removeBtn.innerHTML = '<i class="fas fa-trash"></i>';
        removeBtn.addEventListener('click', function() {
            row.remove();
        });
        actionCell.appendChild(removeBtn);
        
        row.appendChild(codeCell);
        row.appendChild(nameCell);
        row.appendChild(epcCell);
        row.appendChild(actionCell);
        
        batchItemsTable.appendChild(row);
    }
    
    // Save batch tags
    saveBatchTagBtn.addEventListener('click', function() {
        const form = document.getElementById('batchTagForm');
        if (form.checkValidity()) {
            const rows = batchItemsTable.querySelectorAll('tr');
            
            if (rows.length === 0) {
                showNotification('Lütfen en az bir öğe ekleyin.', 'error');
                return;
            }
            
            const batchData = {
                type: document.getElementById('batchTagType').value,
                template: document.getElementById('batchTagTemplate').value,
                printAfterSave: document.getElementById('batchPrintAfterSave').checked,
                items: []
            };
            
            // Collect items data
            rows.forEach(row => {
                const code = row.cells[0].textContent;
                const name = row.cells[1].textContent;
                const epc = row.querySelector('input').value;
                
                batchData.items.push({ code, name, epc });
            });
            
            // In a real application, you would send this data to a server
            console.log('Batch tag data:', batchData);
            
            // Close modal
            batchTagModal.style.display = 'none';
            
            // Show success notification
            showNotification(`${batchData.items.length} etiket başarıyla kaydedildi.`, 'success');
            
            // If print after save is checked, show print notification
            if (batchData.printAfterSave) {
                setTimeout(() => {
                    showNotification('Etiketler yazdırılıyor...', 'info');
                }, 1000);
            }
        } else {
            form.reportValidity();
        }
    });
    
    // Print Tags Modal
    const printTagsBtn = document.getElementById('printTagsBtn');
    const printTagsModal = document.getElementById('printTagsModal');
    const cancelPrintTagsBtn = document.getElementById('cancelPrintTagsBtn');
    const confirmPrintTagsBtn = document.getElementById('confirmPrintTagsBtn');
    
    // Open print tags modal
    printTagsBtn.addEventListener('click', function() {
        // Reset form
        document.getElementById('printTagsForm').reset();
        
        // Set default values
        document.getElementById('printTagCopies').value = 1;
        
        // Show modal
        printTagsModal.style.display = 'flex';
    });
    
    cancelPrintTagsBtn.addEventListener('click', function() {
        printTagsModal.style.display = 'none';
    });
    
    // Print tags
    confirmPrintTagsBtn.addEventListener('click', function() {
        const form = document.getElementById('printTagsForm');
        if (form.checkValidity()) {
            const printData = {
                type: document.getElementById('printTagType').value,
                items: Array.from(document.getElementById('printTagItems').selectedOptions).map(option => option.value),
                template: document.getElementById('printTagTemplate').value,
                copies: document.getElementById('printTagCopies').value,
                printer: document.getElementById('printTagPrinter').value
            };
            
            if (printData.items.length === 0) {
                showNotification('Lütfen en az bir etiket seçin.', 'error');
                return;
            }
            
            // Check if printer is connected
            const printerStatus = document.querySelector('.device-status:first-child .status-light');
            if (!printerStatus.classList.contains('connected')) {
                showNotification('RFID yazıcı bağlı değil.', 'error');
                return;
            }
            
            // In a real application, you would send this data to a server
            console.log('Print data:', printData);
            
            // Close modal
            printTagsModal.style.display = 'none';
            
            // Show success notification
            showNotification(`${printData.items.length} etiket yazdırılıyor...`, 'info');
        } else {
            form.reportValidity();
        }
    });
    
    // Filter tags
    const filterTagsBtn = document.getElementById('filterTagsBtn');
    filterTagsBtn.addEventListener('click', function() {
        const tagType = document.getElementById('tagTypeFilter').value;
        const tagStatus = document.getElementById('tagStatusFilter').value;
        
        // In a real application, you would filter the data based on these values
        console.log('Filtering tags by:', { tagType, tagStatus });
        
        // Show notification
        showNotification('Filtreler uygulandı.', 'info');
    });
    
    // Scan unassigned tags
    const scanTagsBtn = document.getElementById('scanTagsBtn');
    scanTagsBtn.addEventListener('click', function() {
        // Check if reader is connected
        const readerStatus = document.querySelector('.device-status:last-child .status-light');
        if (!readerStatus.classList.contains('connected')) {
            showNotification('RFID okuyucu bağlı değil.', 'error');
            return;
        }
        
        // Show loading state
        scanTagsBtn.disabled = true;
        scanTagsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Taranıyor...';
        
        // Simulate scanning
        setTimeout(() => {
            // Reset button state
            scanTagsBtn.disabled = false;
            scanTagsBtn.innerHTML = '<i class="fas fa-wifi"></i> Etiketleri Tara';
            
            // Clear existing table
            const unassignedTagsTable = document.getElementById('unassignedTagsTable').querySelector('tbody');
            unassignedTagsTable.innerHTML = '';
            
            // Add some random tags
            for (let i = 0; i < 5; i++) {
                const epc = generateRandomEPC();
                const tid = epc.substring(0, 14);
                const now = new Date();
                const dateStr = `${now.getDate().toString().padStart(2, '0')}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getFullYear()} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
                const rssi = Math.floor(-70 + Math.random() * 20);
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="tag-checkbox"></td>
                    <td>${epc}</td>
                    <td>${tid}</td>
                    <td>${dateStr}</td>
                    <td>${dateStr}</td>
                    <td>${rssi} dBm</td>
                    <td>
                        <button class="btn-icon assign" data-id="${epc}"><i class="fas fa-link"></i></button>
                    </td>
                `;
                
                unassignedTagsTable.appendChild(row);
            }
            
            // Enable assign button
            document.getElementById('assignTagsBtn').disabled = false;
            
            // Show notification
            showNotification('5 etiket bulundu.', 'success');
        }, 3000);
    });
    
    // Select all tags
    const selectAllTags = document.getElementById('selectAllTags');
    selectAllTags.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('#unassignedTagsTable .tag-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Assign tags
    const assignTagsBtn = document.getElementById('assignTagsBtn');
    assignTagsBtn.addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('#unassignedTagsTable .tag-checkbox:checked');
        
        if (checkboxes.length === 0) {
            showNotification('Lütfen en az bir etiket seçin.', 'error');
            return;
        }
        
        // In a real application, this would open an assignment dialog
        showNotification(`${checkboxes.length} etiket atama işlemi başlatıldı.`, 'info');
    });
    
    // Assign single tag
    const assignButtons = document.querySelectorAll('#unassignedTagsTable .btn-icon.assign');
    assignButtons.forEach(button => {
        button.addEventListener('click', function() {
            const epc = this.getAttribute('data-id');
            
            // In a real application, this would open an assignment dialog
            showNotification(`${epc} etiketini atama işlemi başlatıldı.`, 'info');
        });
    });
    
    // View tag
    const viewButtons = document.querySelectorAll('#assignedTagsTable .btn-icon.view');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const epc = this.getAttribute('data-id');
            
            // In a real application, this would open a view dialog
            showNotification(`${epc} etiketi görüntüleniyor.`, 'info');
        });
    });
    
    // Edit tag
    const editButtons = document.querySelectorAll('#assignedTagsTable .btn-icon.edit');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const epc = this.getAttribute('data-id');
            
            // In a real application, you would fetch the tag data and open the edit modal
            // For demo purposes, we'll just show a notification
            showNotification(`${epc} etiketi düzenleniyor.`, 'info');
        });
    });
    
    // Print tag
    const printButtons = document.querySelectorAll('#assignedTagsTable .btn-icon.print');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const epc = this.getAttribute('data-id');
            
            // Check if printer is connected
            const printerStatus = document.querySelector('.device-status:first-child .status-light');
            if (!printerStatus.classList.contains('connected')) {
                showNotification('RFID yazıcı bağlı değil.', 'error');
                return;
            }
            
            // In a real application, you would send a print request to the server
            showNotification(`${epc} etiketi yazdırılıyor...`, 'info');
        });
    });
    
    // Delete tag
    const deleteButtons = document.querySelectorAll('#assignedTagsTable .btn-icon.delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const epc = this.getAttribute('data-id');
            
            // Confirm deletion
            if (confirm(`${epc} etiketini silmek istediğinize emin misiniz?`)) {
                // In a real application, you would send a delete request to the server
                console.log('Deleting tag:', epc);
                
                // Remove row from table
                this.closest('tr').remove();
                
                // Show notification
                showNotification('Etiket başarıyla silindi.', 'success');
            }
        });
    });
    
    // Template actions
    const templateUseButtons = document.querySelectorAll('.template-actions .btn-primary');
    templateUseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            
            // In a real application, this would select the template for use
            showNotification(`${templateId} şablonu seçildi.`, 'success');
        });
    });
    
    const templateEditButtons = document.querySelectorAll('.template-actions .btn-secondary');
    templateEditButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            
            // In a real application, this would open a template editor
            showNotification(`${templateId} şablonu düzenleniyor.`, 'info');
        });
    });
    
    // New template button
    const newTemplateBtn = document.getElementById('newTemplateBtn');
    newTemplateBtn.addEventListener('click', function() {
        // In a real application, this would open a template editor
        showNotification('Yeni şablon oluşturuluyor.', 'info');
    });
});
