<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Etiketleme - RFID Envanter Yö<PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="logo">
                <h2>RFID Sistem</h2>
            </div>
            <ul class="nav-links">
                <li>
                    <a href="../index.html">
                        <i class="fas fa-home"></i>
                        <span>Ana Sayfa</span>
                    </a>
                </li>
                <li>
                    <a href="malzemeler.html">
                        <i class="fas fa-boxes"></i>
                        <span><PERSON><PERSON><PERSON><PERSON></span>
                    </a>
                </li>
                <li>
                    <a href="sayim.html">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Sayım</span>
                    </a>
                </li>
                <li>
                    <a href="ambar-fisi.html">
                        <i class="fas fa-receipt"></i>
                        <span>Ambar Fişi</span>
                    </a>
                </li>
                <li>
                    <a href="satin-alma.html">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Satın Alma</span>
                    </a>
                </li>
                <li>
                    <a href="satis.html">
                        <i class="fas fa-cash-register"></i>
                        <span>Satış</span>
                    </a>
                </li>
                <li class="active">
                    <a href="etiketleme.html">
                        <i class="fas fa-tags"></i>
                        <span>Etiketleme</span>
                    </a>
                </li>
                <li>
                    <a href="ayarlar.html">
                        <i class="fas fa-cog"></i>
                        <span>Ayarlar</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header>
                <div class="header-content">
                    <button class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="search-box">
                        <input type="text" placeholder="Etiket ara...">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="user-info">
                        <span>Hoş Geldiniz, Kullanıcı</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </header>

            <div class="content">
                <div class="page-header">
                    <h1>RFID Etiketleme</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" id="newTagBtn">
                            <i class="fas fa-plus"></i> Yeni Etiket
                        </button>
                        <button class="btn btn-secondary" id="printTagsBtn">
                            <i class="fas fa-print"></i> Etiket Yazdır
                        </button>
                    </div>
                </div>

                <!-- Device Status Cards -->
                <div class="status-cards">
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-print"></i>
                        </div>
                        <div class="status-info">
                            <h3>RFID Yazıcı</h3>
                            <div class="status-indicator">
                                <div class="status-light disconnected" id="printerStatus"></div>
                                <span id="printerStatusText">Bağlı Değil</span>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-secondary" id="connectPrinterBtn">
                            <i class="fas fa-plug"></i> Bağlan
                        </button>
                    </div>

                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <div class="status-info">
                            <h3>RFID Okuyucu</h3>
                            <div class="status-indicator">
                                <div class="status-light disconnected" id="readerStatus"></div>
                                <span id="readerStatusText">Bağlı Değil</span>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-secondary" id="connectReaderBtn">
                            <i class="fas fa-plug"></i> Bağlan
                        </button>
                    </div>

                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="status-info">
                            <h3>Toplam Etiket</h3>
                            <div class="status-number" id="totalTags">0</div>
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="status-info">
                            <h3>Aktif Etiket</h3>
                            <div class="status-number" id="activeTags">0</div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="main-panel">
                    <div class="panel-header">
                        <h2>Etiket Yönetimi</h2>
                        <div class="panel-actions">
                            <button class="btn btn-secondary" id="scanTagsBtn">
                                <i class="fas fa-wifi"></i> Etiketleri Tara
                            </button>
                            <button class="btn btn-secondary" id="refreshTagsBtn">
                                <i class="fas fa-sync"></i> Yenile
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="filters-panel">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="tagTypeFilter">Etiket Tipi:</label>
                                <select id="tagTypeFilter" class="form-control">
                                    <option value="">Tümü</option>
                                    <option value="malzeme">Malzeme</option>
                                    <option value="lokasyon">Lokasyon</option>
                                    <option value="palet">Palet</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="tagStatusFilter">Durum:</label>
                                <select id="tagStatusFilter" class="form-control">
                                    <option value="">Tümü</option>
                                    <option value="aktif">Aktif</option>
                                    <option value="pasif">Pasif</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="dateFilter">Tarih Aralığı:</label>
                                <input type="date" id="dateFromFilter" class="form-control">
                                <span>-</span>
                                <input type="date" id="dateToFilter" class="form-control">
                            </div>
                            <div class="filter-actions">
                                <button class="btn btn-primary" id="filterTagsBtn">
                                    <i class="fas fa-filter"></i> Filtrele
                                </button>
                                <button class="btn btn-secondary" id="clearFiltersBtn">
                                    <i class="fas fa-times"></i> Temizle
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tags Table -->
                    <div class="table-container">
                        <table id="tagsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAllTags"></th>
                                    <th>EPC</th>
                                    <th>Etiket Tipi</th>
                                    <th>Bağlı Öğe</th>
                                    <th>Oluşturma Tarihi</th>
                                    <th>Son Okuma</th>
                                    <th>Durum</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination">
                        <button class="btn-page prev" id="prevPageBtn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="page-numbers" id="pageNumbers">
                            <!-- Page numbers will be generated here -->
                        </div>
                        <button class="btn-page next" id="nextPageBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- New Tag Modal -->
    <div class="modal" id="tagModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Yeni Etiket Oluştur</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="tagForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="tagType">Etiket Tipi *</label>
                            <select id="tagType" class="form-control" required>
                                <option value="">Seçiniz</option>
                                <option value="malzeme">Malzeme</option>
                                <option value="lokasyon">Lokasyon</option>
                                <option value="palet">Palet</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="tagTemplate">Etiket Şablonu *</label>
                            <select id="tagTemplate" class="form-control" required>
                                <option value="">Seçiniz</option>
                                <option value="template1">Standart Etiket</option>
                                <option value="template2">QR Kodlu Etiket</option>
                                <option value="template3">Büyük Etiket</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="tagEPC">EPC Kodu</label>
                        <div class="input-with-button">
                            <input type="text" id="tagEPC" class="form-control" placeholder="Otomatik oluşturulacak">
                            <button type="button" class="btn btn-secondary" id="generateEPCBtn" title="EPC Oluştur">
                                <i class="fas fa-random"></i>
                            </button>
                            <button type="button" class="btn btn-secondary" id="readEPCBtn" title="RFID'den Oku">
                                <i class="fas fa-wifi"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group" id="itemSelectGroup">
                        <label for="tagItem">Bağlanacak Öğe *</label>
                        <div class="input-with-button">
                            <input type="text" id="tagItem" class="form-control" placeholder="Malzeme kodu veya adı" required>
                            <button type="button" class="btn btn-secondary" id="searchItemBtn" title="Öğe Ara">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="tagDescription">Açıklama</label>
                        <textarea id="tagDescription" class="form-control" rows="2" placeholder="İsteğe bağlı açıklama"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Durum</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="tagStatus" class="toggle-input" checked>
                                <label for="tagStatus" class="toggle-label"></label>
                                <span class="toggle-text">Aktif</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Yazdırma</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="printAfterSave" class="toggle-input" checked>
                                <label for="printAfterSave" class="toggle-label"></label>
                                <span class="toggle-text">Kaydettikten sonra yazdır</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelTagBtn">İptal</button>
                <button class="btn btn-primary" id="saveTagBtn">
                    <i class="fas fa-save"></i> Kaydet
                </button>
            </div>
        </div>
    </div>

    <!-- Print Tags Modal -->
    <div class="modal" id="printTagsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Etiket Yazdır</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <form id="printTagsForm">
                    <div class="form-group">
                        <label for="printTagItems">Yazdırılacak Etiketler *</label>
                        <select id="printTagItems" class="form-control" multiple size="6" required>
                            <!-- Options will be loaded dynamically -->
                        </select>
                        <small class="form-text">Ctrl tuşu ile birden fazla etiket seçebilirsiniz</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="printTagCopies">Kopya Sayısı</label>
                            <input type="number" id="printTagCopies" class="form-control" min="1" max="10" value="1" required>
                        </div>
                        <div class="form-group">
                            <label for="printTagPrinter">Yazıcı</label>
                            <select id="printTagPrinter" class="form-control" required>
                                <option value="default">Varsayılan RFID Yazıcı</option>
                                <option value="zebra">Zebra ZT411</option>
                                <option value="sato">SATO CL4NX</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelPrintTagsBtn">İptal</button>
                <button class="btn btn-primary" id="confirmPrintTagsBtn">
                    <i class="fas fa-print"></i> Yazdır
                </button>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script src="../js/pages/etiketleme.js"></script>
</body>
</html>
