document.addEventListener('DOMContentLoaded', function() {
    // API base URL
    const API_BASE_URL = 'http://localhost:5000';

    // Modal functionality (optional elements)
    const addMaterialBtn = document.getElementById('addMaterialBtn');
    const materialModal = document.getElementById('materialModal');
    const closeModalBtn = document.querySelector('.close-modal');
    const cancelMaterialBtn = document.getElementById('cancelMaterialBtn');
    const saveMaterialBtn = document.getElementById('saveMaterialBtn');
    const materialForm = document.getElementById('materialForm');

    // Materials data fetching
    const materialsTable = document.getElementById('materialsTable');
    const firmNrSelect = document.getElementById('firmNrSelect');
    const filterBtn = document.getElementById('filterBtn');

    // Filter elements
    const categoryFilter = document.getElementById('categoryFilter');
    const statusFilter = document.getElementById('statusFilter');
    const stockFilter = document.getElementById('stockFilter');

    // Pagination variables
    let currentPage = 1;
    let totalPages = 1;
    let allMaterials = [];
    let filteredMaterials = [];
    const itemsPerPage = 50; // Show 50 items per page

    // Search functionality
    const searchInput = document.querySelector('.search-box input');
    const searchClearBtn = document.querySelector('.search-clear');

    // Initialize page
    initializePage();

    // Search on input
    searchInput.addEventListener('input', function() {
        searchMaterials(this.value);

        // Show/hide clear button based on input
        if (this.value.trim() !== '') {
            searchClearBtn.style.display = 'flex';
        } else {
            searchClearBtn.style.display = 'none';
        }
    });

    // Clear search
    searchClearBtn.addEventListener('click', function() {
        searchInput.value = '';
        searchMaterials('');
        this.style.display = 'none';
        searchInput.focus();
    });

    // Initially hide clear button
    searchClearBtn.style.display = 'none';

    // Filter button event
    filterBtn.addEventListener('click', function() {
        applyFilters();
    });

    // Initialize the page
    function initializePage() {
        console.log('Initializing page...');
        loadFirmNumbers();
        loadAllMaterials();
    }

    // Load firm numbers for dropdown
    function loadFirmNumbers() {
        fetch(`${API_BASE_URL}/api/firm-numbers`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateFirmDropdown(data.data);
                } else {
                    console.error('Failed to load firm numbers:', data.message);
                    showNotification('Firma numaraları yüklenemedi.', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading firm numbers:', error);
                showNotification('Firma numaraları yüklenirken hata oluştu.', 'error');
            });
    }

    // Populate firm dropdown
    function populateFirmDropdown(firmNumbers) {
        firmNrSelect.innerHTML = '<option value="">Tümü</option>';
        firmNumbers.forEach(firmNr => {
            const option = document.createElement('option');
            option.value = firmNr;
            option.textContent = firmNr;
            firmNrSelect.appendChild(option);
        });
    }

    // Load all materials
    function loadAllMaterials() {
        console.log('Loading all materials...');

        // Show loading state
        const tbody = materialsTable.querySelector('tbody');
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin"></i> Yükleniyor...</td></tr>';
        }

        console.log('Fetching from:', `${API_BASE_URL}/api/all-materials`);

        fetch(`${API_BASE_URL}/api/all-materials`)
            .then(response => {
                console.log('Response received:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Data received:', data);
                if (data.success) {
                    allMaterials = data.data;
                    filteredMaterials = [...allMaterials];
                    currentPage = 1;
                    console.log('Materials loaded:', allMaterials.length);
                    updatePagination();
                    displayMaterialsForCurrentPage();
                } else {
                    console.error('Failed to load materials:', data.message);
                    if (tbody) tbody.innerHTML = '<tr><td colspan="9" class="text-center">Malzemeler yüklenemedi.</td></tr>';
                    showNotification('Malzemeler yüklenemedi.', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading materials:', error);
                if (tbody) tbody.innerHTML = '<tr><td colspan="9" class="text-center">Malzemeler yüklenirken hata oluştu.</td></tr>';
                showNotification('Malzemeler yüklenirken hata oluştu.', 'error');
            });
    }

    // Apply filters
    function applyFilters() {
        const firmNr = firmNrSelect.value;
        const category = categoryFilter.value;
        const status = statusFilter.value;
        const stockStatus = stockFilter.value;

        filteredMaterials = allMaterials.filter(material => {
            // Firm number filter
            if (firmNr && material.FIRMNR != firmNr) {
                return false;
            }

            // Stock status filter
            if (stockStatus) {
                const onhand = parseFloat(material.ONHAND) || 0;
                switch (stockStatus) {
                    case 'negatif':
                        if (onhand >= 0) return false;
                        break;
                    case 'kritik':
                        if (onhand >= 100 || onhand < 0) return false;
                        break;
                    case 'yuksek':
                        if (onhand <= 1000) return false;
                        break;
                }
            }

            // Category filter (placeholder - you can implement based on your data)
            if (category) {
                // This would need to be implemented based on how you categorize materials
                // For now, we'll skip this filter
            }

            // Status filter (placeholder - you can implement based on your data)
            if (status) {
                // This would need to be implemented based on how you define status
                // For now, we'll skip this filter
            }

            return true;
        });

        currentPage = 1;
        updatePagination();
        displayMaterialsForCurrentPage();
    }

    // Get stock status class for row coloring
    function getStockStatusClass(onhand) {
        const quantity = parseFloat(onhand) || 0;
        if (quantity < 0) {
            return 'material-row-negative';
        } else if (quantity < 100) {
            return 'material-row-critical';
        } else if (quantity > 1000) {
            return 'material-row-high';
        }
        return '';
    }

    // Get stock amount class for text coloring
    function getStockAmountClass(onhand) {
        const quantity = parseFloat(onhand) || 0;
        if (quantity < 0) {
            return 'negative-amount';
        } else if (quantity < 100) {
            return 'critical-amount';
        } else if (quantity > 1000) {
            return 'high-amount';
        }
        return '';
    }

    // Open modal (only if elements exist)
    if (addMaterialBtn && materialModal) {
        addMaterialBtn.addEventListener('click', function() {
            materialModal.style.display = 'flex';
            // Reset form
            if (materialForm) materialForm.reset();
            const modalHeader = document.querySelector('.modal-header h3');
            if (modalHeader) modalHeader.textContent = 'Yeni Malzeme Ekle';
        });
    }

    // Close modal functions
    function closeModal() {
        if (materialModal) {
            materialModal.style.display = 'none';
        }
    }

    if (closeModalBtn) closeModalBtn.addEventListener('click', closeModal);
    if (cancelMaterialBtn) cancelMaterialBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    if (materialModal) {
        window.addEventListener('click', function(event) {
            if (event.target === materialModal) {
                closeModal();
            }
        });
    }

    // Save material (only if elements exist)
    if (saveMaterialBtn) {
        saveMaterialBtn.addEventListener('click', function() {
            // Simple validation - check if form exists
            if (materialForm) {
            // Get form data
            const materialData = {
                code: document.getElementById('materialCode').value,
                name: document.getElementById('materialName').value,
                category: document.getElementById('materialCategory').value,
                stock: document.getElementById('materialStock').value,
                unit: document.getElementById('materialUnit').value,
                price: document.getElementById('materialPrice').value,
                status: document.getElementById('materialStatus').value,
                description: document.getElementById('materialDescription').value
            };

            // In a real application, you would send this data to a server
            console.log('Material data:', materialData);

            // Close modal
            closeModal();

            // Show success notification
            showNotification('Malzeme başarıyla kaydedildi.', 'success');
            }
        });
    }

    // Edit material
    const editButtons = document.querySelectorAll('.btn-icon.edit');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const materialId = this.getAttribute('data-id');

            // In a real application, you would fetch the material data from the server
            // For demo purposes, we'll use mock data
            const materialData = {
                code: materialId,
                name: 'Demo Malzeme',
                category: 'elektronik',
                stock: 100,
                unit: 'adet',
                price: 150.00,
                status: 'aktif',
                description: 'Bu bir demo malzemedir.'
            };

            // Fill the form with material data
            document.getElementById('materialCode').value = materialData.code;
            document.getElementById('materialName').value = materialData.name;
            document.getElementById('materialCategory').value = materialData.category;
            document.getElementById('materialStock').value = materialData.stock;
            document.getElementById('materialUnit').value = materialData.unit;
            document.getElementById('materialPrice').value = materialData.price;
            document.getElementById('materialStatus').value = materialData.status;
            document.getElementById('materialDescription').value = materialData.description;

            // Change modal title
            document.querySelector('.modal-header h3').textContent = 'Malzeme Düzenle';

            // Open modal
            materialModal.style.display = 'flex';
        });
    });

    // Delete material
    const deleteButtons = document.querySelectorAll('.btn-icon.delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const materialId = this.getAttribute('data-id');

            // Confirm deletion
            if (confirm(`${materialId} kodlu malzemeyi silmek istediğinize emin misiniz?`)) {
                // In a real application, you would send a delete request to the server
                console.log('Deleting material:', materialId);

                // Show success notification
                showNotification('Malzeme başarıyla silindi.', 'success');
            }
        });
    });

    // View material details
    const viewButtons = document.querySelectorAll('.btn-icon.view');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const materialId = this.getAttribute('data-id');

            // In a real application, you would fetch the material details from the server
            // For demo purposes, we'll just show a notification
            showNotification(`${materialId} kodlu malzeme detayları görüntüleniyor.`, 'info');
        });
    });

    // Filter functionality
    const filterBtn = document.getElementById('filterBtn');
    filterBtn.addEventListener('click', function() {
        const category = document.getElementById('categoryFilter').value;
        const status = document.getElementById('statusFilter').value;
        const stock = document.getElementById('stockFilter').value;

        // In a real application, you would filter the data based on these values
        console.log('Filtering by:', { category, status, stock });

        // Show notification
        showNotification('Filtreler uygulandı.', 'info');
    });

    // Update pagination based on filtered materials
    function updatePagination() {
        totalPages = Math.ceil(filteredMaterials.length / itemsPerPage);
        setupPagination();
    }

    // Pagination functionality
    function setupPagination() {
        const paginationContainer = document.querySelector('.pagination');
        const pageNumbersContainer = document.querySelector('.page-numbers');

        // Clear existing page numbers
        pageNumbersContainer.innerHTML = '';

        // Calculate total pages
        totalPages = Math.ceil(filteredMaterials.length / itemsPerPage);

        // Add page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageButton = document.createElement('button');
            pageButton.className = 'btn-page' + (i === currentPage ? ' active' : '');
            pageButton.textContent = i;
            pageButton.addEventListener('click', function() {
                currentPage = i;
                displayMaterialsForCurrentPage();
                updatePaginationUI();
            });
            pageNumbersContainer.appendChild(pageButton);
        }

        // Update prev/next buttons
        const prevButton = document.querySelector('.btn-page.prev');
        const nextButton = document.querySelector('.btn-page.next');

        prevButton.disabled = currentPage === 1;
        nextButton.disabled = currentPage === totalPages;

        // Add event listeners for prev/next buttons
        prevButton.onclick = function() {
            if (currentPage > 1) {
                currentPage--;
                displayMaterialsForCurrentPage();
                updatePaginationUI();
            }
        };

        nextButton.onclick = function() {
            if (currentPage < totalPages) {
                currentPage++;
                displayMaterialsForCurrentPage();
                updatePaginationUI();
            }
        };

        // Show/hide pagination based on data
        paginationContainer.style.display = totalPages > 1 ? 'flex' : 'none';
    }

    // Update pagination UI to reflect current page
    function updatePaginationUI() {
        const pageButtons = document.querySelectorAll('.page-numbers .btn-page');
        pageButtons.forEach(btn => {
            btn.classList.remove('active');
            if (parseInt(btn.textContent) === currentPage) {
                btn.classList.add('active');
            }
        });

        // Update prev/next buttons
        const prevButton = document.querySelector('.btn-page.prev');
        const nextButton = document.querySelector('.btn-page.next');

        prevButton.disabled = currentPage === 1;
        nextButton.disabled = currentPage === totalPages;
    }

    // Search materials by code or name
    function searchMaterials(searchTerm) {
        searchTerm = searchTerm.toLowerCase().trim();

        if (searchTerm === '') {
            // If search is empty, show all materials
            filteredMaterials = [...allMaterials];
        } else {
            // Filter materials by code or name
            filteredMaterials = allMaterials.filter(material => {
                const itemcode = (material.ITEMCODE || material.ITEMKODU || material.MALZEMEKODU || '').toLowerCase();
                const name = (material.INVNAME || material.STOKADI || material.ITEMNAME || '').toLowerCase();

                return itemcode.includes(searchTerm) || name.includes(searchTerm);
            });
        }

        // Reset to first page and update display
        currentPage = 1;
        setupPagination();
        displayMaterialsForCurrentPage();
    }

    // Display materials for the current page
    function displayMaterialsForCurrentPage() {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, filteredMaterials.length);
        const materialsToDisplay = filteredMaterials.slice(startIndex, endIndex);

        renderMaterialsTable(materialsToDisplay);
    }



    // Function to render the materials table with the given data
    function renderMaterialsTable(materials) {
        const tbody = materialsTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (materials.length === 0) {
            // Show empty state
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = '<td colspan="9" class="text-center">Malzeme bulunamadı.</td>';
            tbody.appendChild(emptyRow);
            return;
        }

        // Add each material to the table
        materials.forEach(material => {
            const row = document.createElement('tr');

            // Get data from the new structure
            const firmnr = material.FIRMNR || '';
            const itemcode = material.ITEMCODE || '';
            const itemname = material.ITEMNAME || '';
            const variantcode = material.VARIANTCODE || '';
            const variantname = material.VARIANTNAME || '';
            const onhand = material.ONHAND || 0;
            const warehouse = material.WAREHOUSE || '';
            const unit = material.UNIT || '';

            // Add row coloring based on stock level
            const stockStatusClass = getStockStatusClass(onhand);
            if (stockStatusClass) {
                row.className = stockStatusClass;
            }

            // Add amount coloring class
            const amountClass = getStockAmountClass(onhand);

            row.innerHTML = `
                <td>${firmnr}</td>
                <td>${itemcode}</td>
                <td>${itemname}</td>
                <td>${variantcode}</td>
                <td>${variantname}</td>
                <td class="${amountClass}">${onhand}</td>
                <td>${warehouse}</td>
                <td>${unit}</td>
                <td>
                    <button class="btn-icon edit" data-id="${itemcode}"><i class="fas fa-edit"></i></button>
                    <button class="btn-icon delete" data-id="${itemcode}"><i class="fas fa-trash"></i></button>
                    <button class="btn-icon view" data-id="${itemcode}"><i class="fas fa-eye"></i></button>
                </td>
            `;

            tbody.appendChild(row);
        });

        // Re-attach event listeners to the new buttons
        attachButtonEventListeners();
    }

    // Function to attach event listeners to the table buttons
    function attachButtonEventListeners() {
        // Edit material
        const editButtons = document.querySelectorAll('.btn-icon.edit');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const materialId = this.getAttribute('data-id');
                // Handle edit action
                showNotification(`${materialId} kodlu malzeme düzenleniyor.`, 'info');
            });
        });

        // Delete material
        const deleteButtons = document.querySelectorAll('.btn-icon.delete');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const materialId = this.getAttribute('data-id');
                // Confirm deletion
                if (confirm(`${materialId} kodlu malzemeyi silmek istediğinize emin misiniz?`)) {
                    showNotification(`${materialId} kodlu malzeme silindi.`, 'success');
                }
            });
        });

        // View material details
        const viewButtons = document.querySelectorAll('.btn-icon.view');
        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const materialId = this.getAttribute('data-id');
                showNotification(`${materialId} kodlu malzeme detayları görüntüleniyor.`, 'info');
            });
        });
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Add close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

});
