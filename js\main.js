document.addEventListener('DOMContentLoaded', function() {
    // Toggle sidebar on mobile
    const menuToggle = document.querySelector('.menu-toggle');
    const sidebar = document.querySelector('.sidebar');

    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        const isClickInsideSidebar = sidebar.contains(event.target);
        const isClickOnMenuToggle = menuToggle.contains(event.target);

        if (!isClickInsideSidebar && !isClickOnMenuToggle && sidebar.classList.contains('active')) {
            sidebar.classList.remove('active');
        }
    });

    // Highlight active menu item
    const currentLocation = window.location.href;
    const menuItems = document.querySelectorAll('.nav-links li a');

    menuItems.forEach(item => {
        if (currentLocation.includes(item.getAttribute('href'))) {
            item.parentElement.classList.add('active');
        }
    });

    // If we're on the index page, highlight the home link
    if (currentLocation.endsWith('/') || currentLocation.endsWith('index.html')) {
        document.querySelector('.nav-links li:first-child').classList.add('active');
    }
});

// Function to show notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    document.body.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Auto-hide after 5 seconds
    const hideTimeout = setTimeout(() => {
        hideNotification(notification);
    }, 5000);

    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        clearTimeout(hideTimeout);
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Function to format date
function formatDate(date) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(date).toLocaleDateString('tr-TR', options);
}

// Function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(amount);
}

// Function to validate form inputs
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;

    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;

            // Add error message if it doesn't exist
            if (!input.nextElementSibling || !input.nextElementSibling.classList.contains('error-message')) {
                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.textContent = 'Bu alan zorunludur';
                input.parentNode.insertBefore(errorMessage, input.nextSibling);
            }
        } else {
            input.classList.remove('error');

            // Remove error message if it exists
            if (input.nextElementSibling && input.nextElementSibling.classList.contains('error-message')) {
                input.nextElementSibling.remove();
            }
        }
    });

    return isValid;
}

// Function to create data table
function createDataTable(tableId, data, columns) {
    const table = document.getElementById(tableId);
    if (!table) return;

    // Clear existing table content
    table.innerHTML = '';

    // Create table header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    columns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column.title;
        headerRow.appendChild(th);
    });

    // Add action column if needed
    if (columns.some(col => col.key === 'actions')) {
        const th = document.createElement('th');
        th.textContent = 'İşlemler';
        headerRow.appendChild(th);
    }

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Create table body
    const tbody = document.createElement('tbody');

    data.forEach(item => {
        const row = document.createElement('tr');

        columns.forEach(column => {
            const td = document.createElement('td');

            if (column.key === 'actions') {
                td.innerHTML = `
                    <button class="btn-icon edit" data-id="${item.id}"><i class="fas fa-edit"></i></button>
                    <button class="btn-icon delete" data-id="${item.id}"><i class="fas fa-trash"></i></button>
                    <button class="btn-icon view" data-id="${item.id}"><i class="fas fa-eye"></i></button>
                `;
            } else if (column.format) {
                td.textContent = column.format(item[column.key]);
            } else {
                td.textContent = item[column.key];
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });

    table.appendChild(tbody);

    // Add event listeners for action buttons
    const editButtons = table.querySelectorAll('.btn-icon.edit');
    const deleteButtons = table.querySelectorAll('.btn-icon.delete');
    const viewButtons = table.querySelectorAll('.btn-icon.view');

    editButtons.forEach(button => {
        button.addEventListener('click', () => {
            const id = button.getAttribute('data-id');
            // Handle edit action
            console.log('Edit item with ID:', id);
        });
    });

    deleteButtons.forEach(button => {
        button.addEventListener('click', () => {
            const id = button.getAttribute('data-id');
            // Handle delete action
            console.log('Delete item with ID:', id);
        });
    });

    viewButtons.forEach(button => {
        button.addEventListener('click', () => {
            const id = button.getAttribute('data-id');
            // Handle view action
            console.log('View item with ID:', id);
        });
    });
}

// API base URL
const API_BASE_URL = 'http://localhost:5000';

// Function to handle SQL connection
function testDatabaseConnection(server, database, username, password, port = '1433', type = 'mssql', connectionString = '') {
    const config = {
        server,
        database,
        username,
        password,
        port,
        type,
        connectionString
    };

    return new Promise((resolve, reject) => {
        fetch(`${API_BASE_URL}/api/db/test-connection`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data);
            } else {
                reject(data);
            }
        })
        .catch(error => {
            reject({ success: false, message: 'Bağlantı hatası: ' + error.message });
        });
    });
}

// Function to save database configuration
function saveDatabaseConfig(config) {
    return new Promise((resolve, reject) => {
        fetch(`${API_BASE_URL}/api/db/save-config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data);
            } else {
                reject(data);
            }
        })
        .catch(error => {
            reject({ success: false, message: 'Kaydetme hatası: ' + error.message });
        });
    });
}

// Function to fetch and display BB_101_ONHAND count
function fetchTotalMaterialsCount() {
    const countElement = document.getElementById('total-materials-count');

    if (!countElement) {
        return; // Element not found, probably not on the main page
    }

    fetch(`${API_BASE_URL}/api/bb-101-onhand-count`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Format the count with thousands separator
                const formattedCount = data.count.toLocaleString('tr-TR');
                countElement.textContent = formattedCount;
            } else {
                console.error('Failed to fetch materials count:', data.message);
                countElement.textContent = 'Hata';
            }
        })
        .catch(error => {
            console.error('Error fetching materials count:', error);
            countElement.textContent = 'Hata';
        });
}

// Function to fetch and display critical stock count
function fetchCriticalStockCount() {
    const countElement = document.getElementById('critical-stock-count');

    if (!countElement) {
        return; // Element not found, probably not on the main page
    }

    fetch(`${API_BASE_URL}/api/critical-stock-count`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Format the count with thousands separator
                const formattedCount = data.count.toLocaleString('tr-TR');
                countElement.textContent = formattedCount;
            } else {
                console.error('Failed to fetch critical stock count:', data.message);
                countElement.textContent = 'Hata';
            }
        })
        .catch(error => {
            console.error('Error fetching critical stock count:', error);
            countElement.textContent = 'Hata';
        });
}

// Function to fetch and display negative stock items
function fetchNegativeStockItems() {
    const loadingElement = document.getElementById('negative-stock-loading');
    const tableContainer = document.getElementById('negative-stock-table-container');
    const emptyElement = document.getElementById('negative-stock-empty');
    const errorElement = document.getElementById('negative-stock-error');
    const tableBody = document.querySelector('#negative-stock-table tbody');

    if (!loadingElement || !tableContainer || !emptyElement || !errorElement || !tableBody) {
        return; // Elements not found, probably not on the main page
    }

    // Show loading state
    loadingElement.style.display = 'block';
    tableContainer.style.display = 'none';
    emptyElement.style.display = 'none';
    errorElement.style.display = 'none';

    fetch(`${API_BASE_URL}/api/negative-stock`)
        .then(response => response.json())
        .then(data => {
            loadingElement.style.display = 'none';

            if (data.success) {
                if (data.data && data.data.length > 0) {
                    // Clear existing table data
                    tableBody.innerHTML = '';

                    // Populate table with negative stock items
                    data.data.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.ITEMNAME || ''}</td>
                            <td>${item.VARIANTNAME || '-'}</td>
                            <td class="negative-amount">${item.ONHAND || 0}</td>
                            <td>${item.WAREHOUSE || ''}</td>
                            <td>${item.UNIT || ''}</td>
                        `;
                        tableBody.appendChild(row);
                    });

                    tableContainer.style.display = 'block';
                } else {
                    // No negative stock items found
                    emptyElement.style.display = 'block';
                }
            } else {
                console.error('Failed to fetch negative stock items:', data.message);
                errorElement.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error fetching negative stock items:', error);
            loadingElement.style.display = 'none';
            errorElement.style.display = 'block';
        });
}

// Call the functions when the page loads (only if we're on the main page)
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the main page by looking for the total-materials-count element
    if (document.getElementById('total-materials-count')) {
        fetchTotalMaterialsCount();
        fetchCriticalStockCount();
        fetchNegativeStockItems();
    }
});
